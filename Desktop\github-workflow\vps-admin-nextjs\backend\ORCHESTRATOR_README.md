# VPS Admin Orchestrator System

## Overview

The VPS Admin system has been enhanced with a sophisticated multi-component orchestrator that replaces the single reactive agent with a more intelligent, step-by-step task execution system.

## Architecture

### Core Components

1. **TaskOrchestrator** - The central "brain" that manages task execution
2. **PlannerAgent** - Breaks down user requests into executable steps
3. **ExecutorAgent** - Converts steps into specific shell commands
4. **RefinerAgent** - Analyzes failures and suggests corrections
5. **SummarizerAgent** - Creates concise summaries of completed steps

### Execution Flow

1. **Planning Phase**: User request is analyzed and broken into structured steps
2. **Execution Phase**: Each step is executed with automatic retry and refinement
3. **Self-Correction**: Failed commands are analyzed and alternative approaches are tried
4. **Progress Tracking**: Detailed progress updates and context management

## Key Features

### Intelligent Task Decomposition
- Automatically breaks complex tasks into logical steps
- Considers dependencies and prerequisites
- Provides clear progress tracking

### Self-Correcting Execution
- Analyzes command failures and suggests fixes
- Tries alternative approaches when commands fail
- Learns from previous attempts within the same step

### Enhanced Context Management
- Maintains main context history of successful steps
- Scoped memory for current step attempts
- Rich progress tracking and metadata

### Security-Aware
- Analyzes commands for security risks
- Categorizes command types (package management, service management, etc.)
- Provides security warnings for potentially dangerous operations

## Configuration

### Environment Variables

```bash
# Enable/disable orchestrator system
USE_ORCHESTRATOR=true

# Maximum retries per step
MAX_RETRIES_PER_STEP=3

# Orchestrator timeout (seconds)
ORCHESTRATOR_TIMEOUT=300
```

### Task-Level Control

You can enable/disable orchestrator mode per task:

```bash
# Enable orchestrator for a specific task
POST /task/{task_id}/orchestrator/enable

# Disable orchestrator for a specific task
POST /task/{task_id}/orchestrator/disable
```

## API Enhancements

### Enhanced Task Status Response

The task status endpoint now includes orchestrator-specific fields:

```json
{
  "task_id": "uuid",
  "status": "EXECUTING",
  "use_orchestrator": true,
  "task_plan": {
    "steps": [
      {
        "step_number": 1,
        "description": "Update package lists",
        "status": "COMPLETED",
        "command": "sudo apt update",
        "summary": "Successfully updated package lists",
        "attempts": 1
      }
    ],
    "total_steps": 5,
    "current_step": 1
  },
  "main_context_history": [
    "Step 1: Successfully updated package lists"
  ],
  "current_step_attempts": 0,
  "final_output": ""
}
```

### New Event Types

The orchestrator system introduces new SSE event types:

- `plan_created` - When a task plan is generated
- `step_start` - When a step begins execution
- `step_complete` - When a step completes successfully
- `task_failed` - When a task fails after all retries
- `task_complete` - When all steps complete successfully

## Backward Compatibility

The system maintains full backward compatibility:

- Legacy tasks continue to work with the original system
- New tasks use the orchestrator by default (configurable)
- All existing API endpoints remain unchanged
- Frontend integration requires no changes

## Benefits

### For Users
- **Clearer Progress**: Step-by-step progress with detailed feedback
- **Better Reliability**: Automatic retry and error correction
- **Enhanced Security**: Built-in security analysis and warnings
- **Improved Transparency**: Clear understanding of what's happening

### For Developers
- **Modular Design**: Specialized agents for different tasks
- **Extensible**: Easy to add new agent types or capabilities
- **Testable**: Individual components can be tested in isolation
- **Maintainable**: Clear separation of concerns

## Example Usage

### Simple Task
```
User: "Install Docker"

Orchestrator Flow:
1. Planning: Breaks into steps (update packages, install prerequisites, add GPG key, etc.)
2. Execution: Executes each step with automatic retry
3. Self-Correction: If a step fails, analyzes error and tries alternative approach
4. Completion: Provides summary of what was accomplished
```

### Complex Task with Failures
```
User: "Set up a web server with SSL"

Orchestrator Flow:
1. Planning: Creates 8-step plan
2. Step 3 fails: "Install Nginx" fails due to missing repository
3. Refiner analyzes: Suggests adding repository first
4. Retry succeeds: Nginx installed successfully
5. Continues with remaining steps
6. Completion: Full web server with SSL configured
```

## Monitoring and Debugging

### Logs
- Detailed logging for each orchestrator component
- Step-by-step execution tracking
- Error analysis and retry attempts

### Status Tracking
- Real-time progress updates
- Detailed step status and attempt history
- Context preservation across steps

## Future Enhancements

- **Learning System**: Learn from successful patterns across tasks
- **Parallel Execution**: Execute independent steps in parallel
- **Rollback Capability**: Automatic rollback on critical failures
- **Custom Agents**: Plugin system for domain-specific agents
