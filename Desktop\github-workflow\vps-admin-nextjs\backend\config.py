"""
Configuration module for VPS AI Admin Backend.
Handles environment variable loading, validation, and configuration setup.
"""

import os
import sys
import dotenv
from typing import Dict, Any


class Config:
    """Configuration class to hold all application settings."""

    def __init__(self):
        # Load environment variables
        dotenv.load_dotenv()

        # Gemini AI Configuration
        self.GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
        self.GEMINI_MODEL_NAME = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")

        # Intent Detection LLM Configuration (Gemma-27b)
        self.INTENT_MODEL_NAME = os.getenv("INTENT_MODEL", "models/gemma-3-27b-it")  # Gemma for intent detection
        self.USE_LLM_INTENT = os.getenv("USE_LLM_INTENT", "true").lower() == "true"  # Can disable LLM intent detection

        # Flash 2.5 Configuration for VPS tasks with thinking
        self.FLASH_MODEL_NAME = os.getenv("FLASH_MODEL", "gemini-2.5-flash-preview-05-20")
        self.USE_THINKING = os.getenv("USE_THINKING", "true").lower() == "true"  # Enable thinking for VPS tasks
        self.MIN_THINKING_PERCENTAGE = int(os.getenv("MIN_THINKING_PERCENTAGE", "50"))  # Minimum percentage to use thinking

        # Orchestrator Configuration
        self.USE_ORCHESTRATOR = os.getenv("USE_ORCHESTRATOR", "true").lower() == "true"  # Enable new orchestrator system
        self.MAX_RETRIES_PER_STEP = int(os.getenv("MAX_RETRIES_PER_STEP", "3"))  # Max retries per step
        self.ORCHESTRATOR_TIMEOUT = int(os.getenv("ORCHESTRATOR_TIMEOUT", "300"))  # Timeout in seconds

        # VPS Connection Configuration
        self.VPS_HOSTNAME = os.getenv("VPS_HOSTNAME")
        self.VPS_PORT = int(os.getenv("VPS_PORT", 22))
        self.VPS_USERNAME = os.getenv("VPS_USERNAME")

        # SSH Authentication Configuration
        self.SSH_PRIVATE_KEY_PATH = os.getenv("SSH_PRIVATE_KEY_PATH") or None
        self.SSH_KEY_PASSWORD = os.getenv("SSH_KEY_PASSWORD") or None
        self.VPS_PASSWORD = os.getenv("VPS_PASSWORD") or None

        # Application Configuration
        self.APP_TITLE = "VPS AI Admin Backend (Enhanced Error Handling)"
        self.APP_VERSION = "1.3.0"
        self.SERVER_HOST = "0.0.0.0"
        self.SERVER_PORT = 8000

        # Validate configuration
        self._validate_config()

    def _validate_config(self):
        """Validate essential configuration parameters."""
        print("Validating configuration...")

        # Validate Gemini API Key
        if not self.GEMINI_API_KEY:
            print("FATAL: GEMINI_API_KEY not found.")
            sys.exit(1)

        # Validate VPS connection parameters
        if not self.VPS_HOSTNAME or not self.VPS_USERNAME:
            print("FATAL: VPS_HOSTNAME and VPS_USERNAME must be set.")
            sys.exit(1)

        # Validate authentication method
        if not self.SSH_PRIVATE_KEY_PATH and not self.VPS_PASSWORD:
            print("FATAL: Authentication Error - Set SSH_PRIVATE_KEY_PATH or VPS_PASSWORD.")
            sys.exit(1)

        # Log authentication method
        if self.SSH_PRIVATE_KEY_PATH and self.VPS_PASSWORD:
            print("INFO: Both key path and password set. Key auth prioritized for SSH connection.")
        elif self.SSH_PRIVATE_KEY_PATH:
            print("INFO: Using SSH Key authentication.")
        elif self.VPS_PASSWORD:
            print("INFO: Using SSH Password authentication.")

        print("Configuration validation completed successfully.")

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)."""
        return {
            "gemini_model": self.GEMINI_MODEL_NAME,
            "intent_model": self.INTENT_MODEL_NAME,
            "vps_hostname": self.VPS_HOSTNAME,
            "vps_port": self.VPS_PORT,
            "vps_username": self.VPS_USERNAME,
            "has_ssh_key": bool(self.SSH_PRIVATE_KEY_PATH),
            "has_password": bool(self.VPS_PASSWORD),
            "app_title": self.APP_TITLE,
            "app_version": self.APP_VERSION
        }


def setup_configuration() -> Config:
    """Setup and return application configuration."""
    print("Loading environment variables...")
    config = Config()
    print("Configuration setup completed.")
    return config
