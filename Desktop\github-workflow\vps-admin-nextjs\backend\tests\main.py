# To run this code you need to install the following dependencies:
# pip install google-genai jsonschema rich
import os
import json
import subprocess
import platform
from typing import List, Dict, Optional
from dataclasses import dataclass, asdict
from google import genai
from google.genai import types
from jsonschema import validate, ValidationError
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

# JSON Schema for task validation
TASK_SCHEMA = {
    "type": "object",
    "properties": {
        "task_name": {"type": "string"},
        "description": {"type": "string"},
        "target_os": {"type": "string", "enum": ["linux", "windows", "macos", "any"]},
        "estimated_time": {"type": "string"},
        "prerequisites": {"type": "array", "items": {"type": "string"}},
        "steps": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "step_number": {"type": "integer"},
                    "description": {"type": "string"},
                    "command": {"type": "string"},
                    "expected_output": {"type": "string"},
                    "error_handling": {"type": "string"},
                    "sudo_required": {"type": "boolean"},
                    "danger_level": {"type": "string", "enum": ["safe", "caution", "dangerous"]}
                },
                "required": ["step_number", "description", "command"]
            }
        }
    },
    "required": ["task_name", "description", "target_os", "steps"]
}

@dataclass
class TaskStep:
    step_number: int
    description: str
    command: str
    expected_output: str = ""
    error_handling: str = ""
    sudo_required: bool = False
    danger_level: str = "safe"

@dataclass
class VPSTask:
    task_name: str
    description: str
    target_os: str
    steps: List[TaskStep]
    estimated_time: str = ""
    prerequisites: List[str] = None
    
    def __post_init__(self):
        if self.prerequisites is None:
            self.prerequisites = []

class AIVPSAdmin:
    def __init__(self, api_key: str = None):
        self.client = genai.Client(
            api_key=api_key or os.environ.get("GEMINI_API_KEY")
        )
        self.model = "gemini-2.5-flash-preview-05-20"
        self.current_os = platform.system().lower()
        
    def generate_task(self, user_request: str, target_os: str = None) -> Optional[VPSTask]:
        """Generate a VPS admin task from user request"""
        if not target_os:
            target_os = self.current_os
            
        console.print(f"[blue]🤖 Generating task for: {user_request}[/blue]")
        console.print(f"[blue]📋 Target OS: {target_os}[/blue]")
        
        prompt = self._create_task_prompt(user_request, target_os)
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task("Generating task...", total=None)
                
                json_response = self._call_gemini(prompt)
                progress.update(task, description="Validating response...")
                
                task_data = self._validate_and_parse_response(json_response)
                if not task_data:
                    return None
                    
                # Convert to VPSTask object
                steps = [TaskStep(**step) for step in task_data['steps']]
                vps_task = VPSTask(
                    task_name=task_data['task_name'],
                    description=task_data['description'],
                    target_os=task_data['target_os'],
                    steps=steps,
                    estimated_time=task_data.get('estimated_time', ''),
                    prerequisites=task_data.get('prerequisites', [])
                )
                
                progress.update(task, description="Task generated successfully!")
                
            return vps_task
            
        except Exception as e:
            console.print(f"[red]❌ Error generating task: {e}[/red]")
            return None
    
    def _create_task_prompt(self, user_request: str, target_os: str) -> str:
        return f"""You are an expert VPS system administrator. Generate a detailed task breakdown for the following request:

REQUEST: {user_request}
TARGET OS: {target_os}

Generate a JSON response with this EXACT structure:
{{
  "task_name": "Brief descriptive name for the task",
  "description": "Detailed explanation of what this task accomplishes",
  "target_os": "{target_os}",
  "estimated_time": "Estimated completion time (e.g., '5-10 minutes')",
  "prerequisites": ["Any software or permissions needed before starting"],
  "steps": [
    {{
      "step_number": 1,
      "description": "Clear description of what this step does",
      "command": "Actual command to run (be specific with paths and options)",
      "expected_output": "What the user should expect to see",
      "error_handling": "What to do if this step fails",
      "sudo_required": true/false,
      "danger_level": "safe/caution/dangerous"
    }}
  ]
}}

REQUIREMENTS:
- Provide 3-10 steps depending on task complexity
- Use proper commands for the target OS
- Include error handling for critical steps
- Mark dangerous operations clearly
- Be specific with file paths and command options
- Include verification steps where appropriate
- Consider security best practices

DANGER LEVELS:
- "safe": No risk to system
- "caution": Could affect system if done wrong
- "dangerous": Could break system or cause data loss

Return ONLY the JSON, no other text."""

    def _call_gemini(self, prompt: str) -> str:
        """Call Gemini API and return response"""
        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=prompt)],
            ),
        ]
        
        config = types.GenerateContentConfig(
            thinking_config=types.ThinkingConfig(thinking_budget=0),
            response_mime_type="application/json",
            temperature=0.2,
            system_instruction=[
                types.Part.from_text(text="""You are a VPS system administrator expert. 
                Generate only valid JSON responses for task breakdowns. 
                Focus on practical, safe, and tested commands."""),
            ],
        )
        
        response = ""
        for chunk in self.client.models.generate_content_stream(
            model=self.model,
            contents=contents,
            config=config,
        ):
            if chunk.text:
                response += chunk.text
                
        return response
    
    def _validate_and_parse_response(self, json_response: str) -> Optional[Dict]:
        """Validate and parse the JSON response"""
        try:
            # Parse JSON
            task_data = json.loads(json_response)
            
            # Validate against schema
            validate(instance=task_data, schema=TASK_SCHEMA)
            
            console.print("[green]✅ Task structure validated successfully[/green]")
            return task_data
            
        except json.JSONDecodeError as e:
            console.print(f"[red]❌ Invalid JSON response: {e}[/red]")
            return None
        except ValidationError as e:
            console.print(f"[red]❌ Schema validation failed: {e.message}[/red]")
            return None
    
    def display_task(self, task: VPSTask):
        """Display task in a formatted way"""
        # Task header
        console.print(Panel.fit(
            f"[bold blue]{task.task_name}[/bold blue]\n"
            f"[dim]{task.description}[/dim]\n\n"
            f"🖥️  Target OS: {task.target_os.upper()}\n"
            f"⏱️  Estimated time: {task.estimated_time}\n"
            f"📝 Total steps: {len(task.steps)}",
            title="📋 Task Overview"
        ))
        
        # Prerequisites
        if task.prerequisites:
            console.print("\n[yellow]📋 Prerequisites:[/yellow]")
            for prereq in task.prerequisites:
                console.print(f"  • {prereq}")
        
        # Steps table
        table = Table(title="\n🔧 Task Steps")
        table.add_column("Step", style="cyan", width=6)
        table.add_column("Description", style="white", width=40)
        table.add_column("Command", style="green", width=50)
        table.add_column("Danger", style="red", width=10)
        
        for step in task.steps:
            danger_color = {
                "safe": "[green]●[/green]",
                "caution": "[yellow]●[/yellow]", 
                "dangerous": "[red]●[/red]"
            }.get(step.danger_level, "[white]●[/white]")
            
            sudo_prefix = "[red]sudo [/red]" if step.sudo_required else ""
            
            table.add_row(
                str(step.step_number),
                step.description,
                f"{sudo_prefix}{step.command}",
                danger_color
            )
        
        console.print(table)
    
    def execute_task(self, task: VPSTask, dry_run: bool = True):
        """Execute task steps (with dry run option)"""
        if dry_run:
            console.print("[yellow]🧪 DRY RUN MODE - Commands will not be executed[/yellow]")
        else:
            console.print("[red]⚠️  LIVE EXECUTION MODE - Commands will be run![/red]")
            confirm = console.input("[yellow]Type 'YES' to continue: [/yellow]")
            if confirm != "YES":
                console.print("[blue]Execution cancelled.[/blue]")
                return
        
        for step in task.steps:
            console.print(f"\n[cyan]Step {step.step_number}: {step.description}[/cyan]")
            
            if step.danger_level == "dangerous":
                console.print("[red]⚠️  DANGEROUS OPERATION![/red]")
                if not dry_run:
                    confirm = console.input("[yellow]Continue? (yes/no): [/yellow]")
                    if confirm.lower() != "yes":
                        console.print("[blue]Step skipped.[/blue]")
                        continue
            
            console.print(f"[green]Command:[/green] {step.command}")
            
            if step.expected_output:
                console.print(f"[dim]Expected output: {step.expected_output}[/dim]")
            
            if not dry_run:
                try:
                    result = subprocess.run(
                        step.command,
                        shell=True,
                        capture_output=True,
                        text=True,
                        timeout=300  # 5 minute timeout
                    )
                    
                    if result.returncode == 0:
                        console.print("[green]✅ Command executed successfully[/green]")
                        if result.stdout:
                            console.print(f"Output: {result.stdout[:200]}...")
                    else:
                        console.print(f"[red]❌ Command failed with code {result.returncode}[/red]")
                        if result.stderr:
                            console.print(f"Error: {result.stderr}")
                        if step.error_handling:
                            console.print(f"[yellow]💡 Error handling: {step.error_handling}[/yellow]")
                            
                except subprocess.TimeoutExpired:
                    console.print("[red]❌ Command timed out[/red]")
                except Exception as e:
                    console.print(f"[red]❌ Execution error: {e}[/red]")
            else:
                console.print("[dim]Would execute in live mode[/dim]")

def main():
    """Main function to demonstrate the AI VPS Admin"""
    console.print("[bold blue]🤖 AI VPS Administrator[/bold blue]")
    console.print("Generate and execute system administration tasks\n")
    
    # Initialize AI Admin
    admin = AIVPSAdmin()
    
    # Example tasks
    example_requests = [
        "Install Docker and set up a basic container",
        "Configure firewall to allow HTTP and HTTPS traffic", 
        "Set up automatic security updates",
        "Install and configure Nginx as reverse proxy",
        "Create a backup script for /home directory"
    ]
    
    console.print("[yellow]Example requests you can try:[/yellow]")
    for i, req in enumerate(example_requests, 1):
        console.print(f"  {i}. {req}")
    
    while True:
        console.print("\n" + "="*60)
        
        # Get user input
        user_request = console.input("\n[green]Enter your VPS admin task (or 'quit'): [/green]")
        
        if user_request.lower() in ['quit', 'exit', 'q']:
            console.print("[blue]Goodbye! 👋[/blue]")
            break
            
        # Optional: specify target OS
        target_os = console.input("[blue]Target OS (linux/windows/macos/current): [/blue]") or "current"
        if target_os == "current":
            target_os = admin.current_os
            
        # Generate task
        task = admin.generate_task(user_request, target_os)
        
        if task:
            # Display task
            admin.display_task(task)
            
            # Ask if user wants to execute
            action = console.input("\n[yellow]Actions: (d)ry run, (e)xecute, (s)kip: [/yellow]").lower()
            
            if action == 'd':
                admin.execute_task(task, dry_run=True)
            elif action == 'e':
                admin.execute_task(task, dry_run=False)
            else:
                console.print("[blue]Task skipped.[/blue]")
        else:
            console.print("[red]Failed to generate task. Please try again.[/red]")

if __name__ == "__main__":
    main()