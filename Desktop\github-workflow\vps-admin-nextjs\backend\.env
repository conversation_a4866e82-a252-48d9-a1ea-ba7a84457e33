GEMINI_API_KEY=AIzaSyAGGfSW98WwUrQkcItxOAYwXvfTQhXsHbk
GEMINI_MODEL=models/gemini-2.5-flash-preview-05-20
# Intent detection using Gemma-27b
INTENT_MODEL=models/gemma-3-27b-it
# Flash 2.5 for VPS tasks with thinking
FLASH_MODEL=gemini-2.5-flash-preview-05-20
# Set to false to disable LLM intent detection and use keyword-based fallback only
USE_LLM_INTENT=true
# Enable thinking for VPS tasks
USE_THINKING=true
# Minimum percentage to use thinking (50%-100% = 0-24576 thinking budget)
MIN_THINKING_PERCENTAGE=50
VPS_HOSTNAME=localhost
VPS_PORT=4646
VPS_USERNAME=root
VPS_PASSWORD=root
# SSH_PRIVATE_KEY_PATH=/path/to/your/id_rsa
