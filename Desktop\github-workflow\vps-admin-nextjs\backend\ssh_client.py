"""
SSH client module for VPS AI Admin Backend.
Handles SSH connections, key management, and command execution.
"""

import asyncio
import traceback
import re
from typing import Optional
import paramiko

# Assuming these are in their respective files
from config import Config
from models import SSHResult


class SSHClient:
    """SSH client for executing commands on remote VPS."""

    def __init__(self, config: Config):
        self.config = config
        self.ssh_key_cache: Optional[paramiko.PKey] = None
        # The password for the key file itself, not the VPS password
        self.ssh_key_password_cache = config.SSH_KEY_PASSWORD

    async def get_ssh_key(self) -> Optional[paramiko.PKey]:
        """Load and cache SSH private key. (Preserving original verbose structure)"""
        if not self.config.SSH_PRIVATE_KEY_PATH:
            return None

        if self.ssh_key_cache:
            return self.ssh_key_cache

        key: Optional[paramiko.PKey] = None
        key_path = self.config.SSH_PRIVATE_KEY_PATH
        key_password = self.ssh_key_password_cache

        print(f"DEBUG: Attempting to load SSH key: {key_path}")
        try:
            # Try loading as RS<PERSON><PERSON><PERSON>
            try:
                key = paramiko.RSAKey.from_private_key_file(key_path, password=key_password)
                print(f"INFO: Loaded SSH key type: {type(key).__name__}")
                self.ssh_key_cache = key
                return key
            except paramiko.SSHException:
                pass  # Not an RSAKey, try next type

            # Try loading as Ed25519Key
            try:
                key = paramiko.Ed25519Key.from_private_key_file(key_path, password=key_password)
                print(f"INFO: Loaded SSH key type: {type(key).__name__}")
                self.ssh_key_cache = key
                return key
            except paramiko.SSHException:
                pass  # Not an Ed25519Key, try next type

            # Try loading as ECDSAKey
            try:
                key = paramiko.ECDSAKey.from_private_key_file(key_path, password=key_password)
                print(f"INFO: Loaded SSH key type: {type(key).__name__}")
                self.ssh_key_cache = key
                return key
            except paramiko.SSHException:
                pass # Not an ECDSAKey, try next type

            # Try loading as DSSKey
            try:
                key = paramiko.DSSKey.from_private_key_file(key_path, password=key_password)
                print(f"INFO: Loaded SSH key type: {type(key).__name__}")
                self.ssh_key_cache = key
                return key
            except paramiko.SSHException:
                pass # Not a DSSKey

            # If no key type worked
            raise paramiko.SSHException(f"Could not load key from {key_path} (unsupported format or bad password).")

        except paramiko.PasswordRequiredException:
            self.ssh_key_cache = None
            if key_password is None:
                raise paramiko.AuthenticationException(
                    f"SSH key {key_path} is encrypted and requires a password (set SSH_KEY_PASSWORD)."
                )
            else:
                raise paramiko.AuthenticationException("Incorrect SSH key passphrase provided.")
        except FileNotFoundError:
            self.ssh_key_cache = None
            raise FileNotFoundError(f"SSH key file not found: {key_path}")
        except Exception as e:
            print(f"ERROR: Loading SSH key: {type(e).__name__}: {e}")
            self.ssh_key_cache = None
            raise

    async def execute_command_async(self, command: str) -> SSHResult:
        """Execute SSH command asynchronously."""
        loop = asyncio.get_running_loop()
        key = None
        auth_method = "none"

        connect_kwargs = {
            "hostname": self.config.VPS_HOSTNAME,
            "port": self.config.VPS_PORT,
            "username": self.config.VPS_USERNAME,
            "timeout": 20,
            "look_for_keys": False,
            "allow_agent": False
        }

        if self.config.SSH_PRIVATE_KEY_PATH:
            try:
                key = await self.get_ssh_key()
                connect_kwargs["pkey"] = key
                connect_kwargs["password"] = None
                auth_method = "key"
                print("DEBUG: SSH Conn: Using Key.")
            except Exception as key_error:
                print(f"WARNING: SSH Key load failed ({type(key_error).__name__}). Trying password for SSH connection.")
                if not self.config.VPS_PASSWORD:
                    return SSHResult(
                        stdout="",
                        stderr=f"SSH Key Error: {key_error}, and no VPS_PASSWORD was provided as a fallback.",
                        exit_status=-1,
                        success=False,
                        command=command
                    )
                connect_kwargs["password"] = self.config.VPS_PASSWORD
                connect_kwargs["pkey"] = None
                auth_method = "password"
                print("DEBUG: SSH Conn: Using Password as fallback.")
        elif self.config.VPS_PASSWORD:
            connect_kwargs["password"] = self.config.VPS_PASSWORD
            connect_kwargs["pkey"] = None
            auth_method = "password"
            print("DEBUG: SSH Conn: Using Password.")
        else:
            return SSHResult(
                stdout="",
                stderr="Internal Auth Error: No valid SSH connection method (no key or password).",
                exit_status=-1,
                success=False,
                command=command
            )

        def run_ssh():
            """Execute SSH command in a separate thread to not block asyncio."""
            import time
            ssh_client: Optional[paramiko.SSHClient] = None
            start_time = time.time()

            try:
                ssh_client = paramiko.SSHClient()
                ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                print(f"DEBUG: Connecting SSH ({auth_method})...")
                ssh_client.connect(**connect_kwargs)
                print("INFO: SSH Connection successful.")
                print(f"INFO: Executing command: {command}")

                env_command = f"export LANG=C.UTF-8 LC_ALL=C.UTF-8 2>/dev/null || export LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8 2>/dev/null || true; {command}"
                
                # =================================================================================
                # THE FIX IS HERE: `get_pty=True`
                # This tells paramiko to request a pseudo-terminal, which makes the remote
                # server behave as if it's an interactive session.
                # =================================================================================
                stdin, stdout, stderr = ssh_client.exec_command(
                    env_command, 
                    timeout=300, 
                    get_pty=True
                )

                def clean_and_decode_output(byte_stream: bytes) -> str:
                    """Decodes byte stream as UTF-8 and removes ANSI escape sequences."""
                    if not byte_stream:
                        return ""
                    decoded_text = byte_stream.decode('utf-8', errors='replace')
                    ansi_escape_pattern = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
                    clean_text = ansi_escape_pattern.sub('', decoded_text)
                    return clean_text.replace('\x00', '')

                stdout_raw = stdout.read()
                stderr_raw = stderr.read()
                
                # Note: When using get_pty=True, stdout and stderr are often combined into the stdout stream.
                # The stderr stream might be empty. This is normal behavior for PTYs.
                stdout_data = clean_and_decode_output(stdout_raw)
                stderr_data = clean_and_decode_output(stderr_raw)

                exit_status = stdout.channel.recv_exit_status()
                execution_time = int((time.time() - start_time) * 1000)

                print(f"DEBUG: Command exit status: {exit_status}")
                if stdout_data:
                    # Carriage returns (\r) are common with PTYs, so we remove them for cleaner logging
                    log_output = stdout_data.replace('\r', '')
                    print(f"DEBUG: Cleaned command stdout:\n{log_output[:500]}...")
                if stderr_data:
                    log_output = stderr_data.replace('\r', '')
                    print(f"DEBUG: Cleaned command stderr:\n{log_output[:500]}...")

                return SSHResult(
                    stdout=stdout_data,
                    stderr=stderr_data,
                    exit_status=exit_status,
                    success=exit_status == 0,
                    command=command,
                    execution_time=execution_time
                )

            except paramiko.AuthenticationException as e:
                print(f"ERROR: SSH Auth Failed during connection ({auth_method}): {e}")
                error_msg = str(e) or f"Authentication failed using {auth_method}."
                return SSHResult(
                    stdout="",
                    stderr=f"SSH Auth Failed: {error_msg}",
                    exit_status=-1,
                    success=False,
                    command=command
                )
            except paramiko.SSHException as e:
                print(f"ERROR: SSH Connection Error: {type(e).__name__}: {e}")
                return SSHResult(
                    stdout="",
                    stderr=f"SSH Connection Error: {e}",
                    exit_status=-1,
                    success=False,
                    command=command
                )
            except Exception as e:
                print(f"ERROR: Unexpected SSH error: {type(e).__name__}: {e}\n{traceback.format_exc()}")
                return SSHResult(
                    stdout="",
                    stderr=f"An unexpected error occurred: {e}",
                    exit_status=-1,
                    success=False,
                    command=command
                )
            finally:
                if ssh_client and ssh_client.get_transport() and ssh_client.get_transport().is_active():
                    ssh_client.close()
                    print("DEBUG: SSH connection closed.")

        print("DEBUG: Scheduling SSH command...")
        result = await loop.run_in_executor(None, run_ssh)
        print("DEBUG: SSH command result received.")
        return result