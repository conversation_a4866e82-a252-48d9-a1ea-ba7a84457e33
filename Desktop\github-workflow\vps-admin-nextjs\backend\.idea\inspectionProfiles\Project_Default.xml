<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="201" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="33">
            <item index="0" class="java.lang.String" itemvalue="chromadb" />
            <item index="1" class="java.lang.String" itemvalue="protobuf" />
            <item index="2" class="java.lang.String" itemvalue="azure_storage" />
            <item index="3" class="java.lang.String" itemvalue="aim" />
            <item index="4" class="java.lang.String" itemvalue="jnius" />
            <item index="5" class="java.lang.String" itemvalue="pysqlcipher3" />
            <item index="6" class="java.lang.String" itemvalue="Numeric" />
            <item index="7" class="java.lang.String" itemvalue="tensorflow" />
            <item index="8" class="java.lang.String" itemvalue="mtrand" />
            <item index="9" class="java.lang.String" itemvalue="numarray" />
            <item index="10" class="java.lang.String" itemvalue="pickle5" />
            <item index="11" class="java.lang.String" itemvalue="guardrails" />
            <item index="12" class="java.lang.String" itemvalue="tensorflow_text" />
            <item index="13" class="java.lang.String" itemvalue="docutils" />
            <item index="14" class="java.lang.String" itemvalue="truststore" />
            <item index="15" class="java.lang.String" itemvalue="uvloop" />
            <item index="16" class="java.lang.String" itemvalue="tokio" />
            <item index="17" class="java.lang.String" itemvalue="importlib_metadata" />
            <item index="18" class="java.lang.String" itemvalue="wincertstore" />
            <item index="19" class="java.lang.String" itemvalue="xmlrpclib" />
            <item index="20" class="java.lang.String" itemvalue="botocore" />
            <item index="21" class="java.lang.String" itemvalue="boto3" />
            <item index="22" class="java.lang.String" itemvalue="sqlcipher3" />
            <item index="23" class="java.lang.String" itemvalue="cerebrium" />
            <item index="24" class="java.lang.String" itemvalue="vespa" />
            <item index="25" class="java.lang.String" itemvalue="langchain-agents" />
            <item index="26" class="java.lang.String" itemvalue="langchain-llms" />
            <item index="27" class="java.lang.String" itemvalue="Flask" />
            <item index="28" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="29" class="java.lang.String" itemvalue="flask" />
            <item index="30" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="31" class="java.lang.String" itemvalue="gunicorn" />
            <item index="32" class="java.lang.String" itemvalue="caprover-api" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E501" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N801" />
          <option value="N802" />
          <option value="N806" />
          <option value="N803" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>