# VPS Admin AI Backend Documentation

This document provides a comprehensive overview of the VPS Admin AI backend project, detailing its architecture, configuration, API, and core components.

## 1. Project Overview

The VPS Admin AI is a sophisticated backend system designed to act as an AI-powered Linux system administrator. It accepts natural language requests from a user, intelligently detects their intent, and translates those requests into a series of executable shell commands to be run on a remote Virtual Private Server (VPS).

The system features intelligent intent detection and multiple operational modes:
1.  **Smart Intent Detection**: Uses Gemma-27b to classify user messages as either casual chat or VPS administration tasks
2.  **Chat Mode**: Provides direct conversational responses for casual interactions without system administration
3.  **Legacy Reactive Mode**: A single AI model determines the next command based on conversation history and command outcomes
4.  **Advanced Orchestrator Mode**: A multi-agent system that plans, executes, and self-corrects complex tasks with enhanced reasoning capabilities

Key innovations include:
- **Thinking Budget System**: Complex VPS tasks (>50% difficulty) leverage Flash 2.5's enhanced reasoning capabilities
- **Intelligent Routing**: Chat messages get immediate responses while VPS tasks go through structured planning
- **Adaptive Resource Allocation**: Computational resources are allocated based on task complexity

The backend is built with FastAPI, communicates with clients via Server-Sent Events (SSE) for real-time updates, and uses Google's Gemini family of models including Gemma-27b for intent detection and Flash 2.5 for complex reasoning.

## 2. Architecture

### Core Components

The project is modular, with each component having a distinct responsibility:

-   **FastAPI App (`main.py`):** The main web server that exposes API endpoints for starting tasks, sending messages, and checking status.
-   **Task Manager (`task_manager.py`):** An in-memory database that creates, stores, and manages the state of each user task. It tracks progress, history, and orchestrator-specific data.
-   **Stream Processor (`stream_processor.py`):** The central hub for handling incoming user messages. It routes requests to either the legacy system or the new Orchestrator based on configuration.
-   **Task Orchestrator (`orchestrator.py`):** The "brain" of the advanced mode. It manages the entire lifecycle of a task, from planning to execution and completion, coordinating between specialized AI agents.
-   **AI Agents (`agents.py`):** A suite of specialized AI models, each designed for a specific sub-task (e.g., planning, refining failed commands, summarizing results).
-   **AI Client (`ai_client.py`):** A client for interacting with the Google Gemini & Gemma APIs, handling prompt construction, intent detection, and response generation.
-   **SSH Client (`ssh_client.py`):** A Paramiko-based client responsible for securely connecting to the remote VPS and executing commands.
-   **System Scanner (`system_scanner.py`):** A utility that performs an initial scan of the remote VPS to gather context like OS version, running services, and installed packages.

### Enhanced Execution Flow (Orchestrator Mode)

1.  **Task Initiation:** A user sends an initial request (e.g., "Install Docker and deploy my app from GitHub"). The `/start_task` endpoint creates a new task in the `TaskManager`.
2.  **System Scan:** The `SystemScanner` connects to the VPS to gather initial system information.
3.  **Intent Detection:** The `TaskOrchestrator` uses Gemma-27b to analyze the user's message:
    -   **Chat Intent:** Direct conversational response without system administration (e.g., "How are you?")
    -   **VPS Intent:** System administration task with difficulty percentage (0%-100%)
4.  **Smart Routing:**
    -   **Chat Messages:** Immediate response and task completion
    -   **VPS Tasks:** Proceed to planning phase with difficulty-based resource allocation
5.  **Enhanced Planning Phase:** The `TaskOrchestrator` invokes the `PlannerAgent` with thinking budget:
    -   **Simple Tasks (<50% difficulty):** Standard planning with regular AI generation
    -   **Complex Tasks (≥50% difficulty):** Enhanced planning using Flash 2.5 with thinking budget for improved reasoning
6.  **Execution Phase:** The `TaskOrchestrator` begins executing the plan step-by-step.
7.  **Command Confirmation:** For each step, a command is generated. The system sends this command to the user for confirmation (`yes`/`no`).
8.  **Execution & Self-Correction:**
    -   If the user confirms, the `SSHClient` executes the command.
    -   **On Success:** The `SummarizerAgent` creates a brief summary of what was accomplished, which is added to the context history. The orchestrator moves to the next step.
    -   **On Failure:** The `RefinerAgent` is invoked. It analyzes the error message, stdout, and stderr to propose a corrected command or an alternative approach. The orchestrator then asks the user for confirmation on this new command. This loop continues for a configured number of retries.
9.  **Task Completion:** Once all steps are successfully completed, the orchestrator marks the task as `COMPLETED`. If a step fails repeatedly and cannot be corrected, the task is marked as `FAILED`.

## 3. Setup and Configuration

### 3.1. File Structure

```
backend/
├── .env                           # Environment variables for configuration
├── agents.py                      # Specialized AI agents (Planner, Executor, etc.)
├── ai_client.py                   # Client for interacting with Gemini/Gemma AI
├── config.py                      # Configuration loader
├── main.py                        # FastAPI application entry point
├── models.py                      # Pydantic data models
├── orchestrator.py                # Core task orchestrator engine
├── ORCHESTRATOR_README.md         # README for the orchestrator system
├── THINKING_BUDGET_IMPLEMENTATION.md # Documentation for thinking budget system
├── requirements.txt               # Python dependencies
├── ssh_client.py                  # SSH client for remote command execution
├── stream_processor.py            # SSE stream processing logic
└── system_scanner.py              # Initial remote system scanner
```

### 3.2. Dependencies

The project's dependencies are listed in `requirements.txt`:

```
fastapi
uvicorn[standard]>=0.24.0
sse-starlette
pydantic
python-dotenv
google-generativeai
paramiko
google-genai
```

Install them using: `pip install -r requirements.txt`

### 3.3. Environment Variables (`.env`)

The application is configured via a `.env` file.

| Variable                  | Description                                                                                             | Example                                         |
| ------------------------- | ------------------------------------------------------------------------------------------------------- | ----------------------------------------------- |
| `GEMINI_API_KEY`          | **Required.** Your Google AI API key.                                                                   | `AIzaSy...`                                     |
| `GEMINI_MODEL`            | The main Gemini model used for general tasks in the legacy system.                                      | `gemini-1.5-flash`                             |
| `INTENT_MODEL`            | The model used for intent detection (chat vs. VPS task). Gemma-27b is recommended.                     | `models/gemma-3-27b-it`                         |
| `FLASH_MODEL`             | Flash 2.5 model for complex VPS tasks that benefit from enhanced reasoning with thinking budget.        | `gemini-2.5-flash-preview-05-20`                |
| `USE_LLM_INTENT`          | `true` to use the `INTENT_MODEL` for intent detection, `false` to use keyword-based fallback.          | `true`                                          |
| `USE_THINKING`            | `true` to enable the "thinking" feature of the Flash model for complex reasoning.                       | `true`                                          |
| `MIN_THINKING_PERCENTAGE` | The task difficulty threshold (0-100) above which the "thinking" feature is enabled.                   | `50`                                            |
| `USE_ORCHESTRATOR`        | `true` to enable the advanced orchestrator system, `false` for legacy mode.                            | `true`                                          |
| `MAX_RETRIES_PER_STEP`    | Maximum number of retry attempts per step in orchestrator mode.                                         | `3`                                             |
| `ORCHESTRATOR_TIMEOUT`    | Timeout in seconds for orchestrator operations.                                                         | `300`                                           |
| `VPS_HOSTNAME`            | **Required.** The hostname or IP address of the remote VPS.                                             | `localhost` or `*************`                  |
| `VPS_PORT`                | The SSH port of the remote VPS.                                                                         | `22` or `4646`                                  |
| `VPS_USERNAME`            | **Required.** The username for the SSH connection.                                                      | `root`                                          |
| `VPS_PASSWORD`            | The password for the SSH connection. **Required if `SSH_PRIVATE_KEY_PATH` is not set.**                | `root`                                          |
| `SSH_PRIVATE_KEY_PATH`    | The local path to your SSH private key file. **Required if `VPS_PASSWORD` is not set.**                | `/home/<USER>/.ssh/id_rsa`                        |

## 4. API Endpoints

All endpoints are defined in `main.py`.

---

#### `POST /start_task`

Initiates a new task session. It performs an initial system scan and sets up the AI chat session.

-   **Request Body:** `StartRequest`
    ```json
    {
      "initial_prompt": "Install nginx on my server.",
      "task_metadata": {
        "title": "Nginx Installation Task",
        "priority": "high"
      }
    }
    ```
-   **Response:**
    ```json
    {
      "task_id": "a1b2c3d4-e5f6-..."
    }
    ```

---

#### `POST /send_message`

Sends a message to an existing task and receives a real-time stream of events (SSE). This is the primary endpoint for user interaction.

-   **Request Body:** `MessageRequest`
    ```json
    {
      "message": "yes",
      "task_id": "a1b2c3d4-e5f6-..."
    }
    ```
-   **Response:** An `EventSourceResponse` stream containing JSON-encoded events.

---

#### `GET /task/{task_id}`

Retrieves the current status, history, and metadata for a specific task.

-   **Response Body:** `TaskStatusResponse` (a detailed JSON object).

---

#### `DELETE /task/{task_id}`

Deletes a specific task from the `TaskManager`.

-   **Response:**
    ```json
    {
      "task_id": "a1b2c3d4-e5f6-...",
      "message": "Task deleted successfully"
    }
    ```

---

#### `GET /`

A basic health check endpoint.

---

#### Debug & Recovery Endpoints

-   `POST /task/{task_id}/reset`: Resets a task that is stuck in a processing state.
-   `POST /tasks/recover-stuck`: Automatically finds and resets all stuck tasks.
-   `GET /tasks/debug`: Returns a debug summary of all active tasks.
-   `POST /task/{task_id}/force-status/{new_status}`: Manually sets the status of a task.
-   `POST /task/{task_id}/orchestrator/{mode}`: Enables or disables orchestrator mode (`enable`/`disable`) for a specific task.

## 5. Core Components Documentation

### 5.1. `main.py` - FastAPI Application

This file sets up the FastAPI application, configures CORS middleware, initializes all core components (`TaskManager`, `StreamProcessor`), and defines the API endpoints listed above. It serves as the entry point for the entire backend service.

### 5.2. `config.py` - Configuration Management

-   **`Config` class:** This class loads environment variables from the `.env` file using `python-dotenv`. It performs validation on startup to ensure that critical variables (like `GEMINI_API_KEY` and VPS connection details) are present, exiting gracefully if they are not. It provides a centralized, type-safe way to access configuration throughout the application.

### 5.3. `task_manager.py` - Task Lifecycle Management

-   **`TaskManager` class:** This class acts as an in-memory repository for all tasks.
    -   `tasks: Dict[str, TaskEntry]`: A dictionary holding all task objects, keyed by their UUID.
    -   `create_task()`: Creates a new `TaskEntry` object with a unique ID, initializes its state, and stores it.
    -   `initialize_task()`: A critical asynchronous method that orchestrates the initial system scan and AI chat session setup for a new task.
    -   `get_task()`, `update_task_status()`, etc.: A suite of methods for CRUD operations and state management of tasks.
    -   **Orchestrator Support:** Includes methods specifically for the orchestrator, such as `should_use_orchestrator()`, `update_task_plan()`, and managing context history (`main_context_history`).
    -   **Recovery Methods:** Provides functions like `reset_stuck_task()` and `get_stuck_tasks()` for debugging and recovering tasks that may have frozen due to an unexpected error.

### 5.4. `ai_client.py` - Enhanced AI Interaction Client

-   **`AIClient` class:** This is the primary interface to the Google AI models with advanced intent detection and thinking capabilities.
    -   `_initialize_gemini()`: Configures the Gemini API key and initializes the generative models.
    -   `_initialize_flash_client()`: Initializes the Flash 2.5 client for enhanced reasoning with thinking budget.
    -   `_initialize_intent_model()`: Sets up the Gemma-27b model specifically for intent detection.
    -   **Intent Detection System:**
        -   `_detect_user_intent_with_percentage()`: Core method using Gemma-27b to classify user input as "chat" or "vps" tasks with difficulty percentage (0%-100%). Returns tuple of (intent_type, percentage, chat_response).
        -   `_fallback_intent_detection()`: Keyword-based fallback when LLM intent detection fails or is disabled.
        -   **Response Formats:**
            -   Chat: `#Chat:(direct response message)`
            -   VPS: `#VPS:(0%-100%)` where percentage indicates task complexity
    -   **Enhanced Generation Methods:**
        -   `generate_response_with_thinking()`: Uses Flash 2.5 with thinking budget for complex tasks (≥50% difficulty). Thinking budget calculated as: `(percentage - 50) / 50 * 24576`
        -   `generate_response()`: Standard method for regular AI generation
        -   `_calculate_thinking_budget()`: Maps difficulty percentage to thinking budget (0-24576)
    -   `build_ai_prompt()`: Constructs context-rich prompts with system information, conversation history, and security guidelines.
    -   `update_chat_history()`: Maintains Gemini chat history for conversation context.

### 5.5. `orchestrator.py` - Enhanced Task Orchestrator Engine

-   **`TaskOrchestrator` class:** The central coordinator for the multi-agent system with intelligent intent routing.
    -   **Enhanced Execution Flow:**
        -   `execute_task()`: Main orchestration loop with three phases:
            -   **Phase 0:** Intent detection using Gemma-27b
            -   **Phase 1:** Planning with thinking budget allocation
            -   **Phase 2:** Execution with error recovery
    -   **Intent Handling:**
        -   `_handle_chat_response()`: Processes chat intents directly without planning/execution
        -   Smart routing based on intent detection results
    -   **Enhanced Planning:**
        -   `_planning_phase()`: Invokes `PlannerAgent` with difficulty percentage for thinking budget allocation
        -   Uses Flash 2.5 with thinking for complex tasks (≥50% difficulty)
    -   **Execution Management:**
        -   `_execution_phase()`: Iterates through plan steps with command confirmation
        -   `_handle_step_execution()`: Executes individual steps with SSH and result processing
        -   `_handle_step_failure()`: Error recovery using `RefinerAgent` for command corrections

### 5.6. `ssh_client.py` - Secure Shell (SSH) Client

-   **`SSHClient` class:** A wrapper around the `paramiko` library for remote command execution.
    -   `execute_command_async()`: The core method that connects to the remote VPS and executes a given command. It runs the blocking `paramiko` call in a separate thread to avoid blocking the asyncio event loop.
    -   **Authentication:** It intelligently handles authentication, prioritizing SSH key-based auth if a key path is provided, and falling back to password-based auth.
    -   **Error Handling:** It robustly handles various connection and authentication errors.
    -   **Result Formatting:** It returns a structured `SSHResult` model containing the command's stdout, stderr, exit status, and success flag.

### 5.7. `system_scanner.py` - Initial System Analysis

-   **`SystemScanner` class:** Responsible for gathering initial intelligence about the remote server.
    -   `perform_initial_scan()`: The main method that runs a sequence of commands on the remote server to determine:
        -   **OS Information:** Uses `cat /etc/os-release` or `uname` to find the OS distribution and version.
        -   **Running Services:** Uses `systemctl` or `service --status-all` to list active services.
        -   **Installed Packages:** Uses `dpkg-query` (for Debian/Ubuntu) or `rpm` (for CentOS/RHEL) to get a list of installed packages.
    -   The results are formatted into a single string that is provided as initial context to the AI.

### 5.8. `stream_processor.py` - SSE Stream Handling

-   **`StreamProcessor` class:** This class contains the main logic for handling a user's message once a task has been started.
    -   `process_stream()`: The main async generator function that receives a `task_id` and `user_message`. It acts as a state machine.
    -   **Routing:** It checks if the task is configured to use the Orchestrator. If so, it delegates control to `_handle_orchestrator_flow`. Otherwise, it proceeds with the legacy state machine.
    -   `_handle_confirmation_state()`: (Legacy) Handles the user's `yes`/`no` response to a command confirmation.
    -   `_handle_input_state()`: (Legacy) Processes a new user message by calling the AI for the next action.
    -   `_execute_command()`: (Legacy) A helper that calls the `SSHClient` and then passes the result back to the AI for the next step.
    -   `_ask_ai_and_process()`: (Legacy) The core loop of the legacy system: build prompt, get AI response, process response (is it a command, question, or `TASK_COMPLETE`?).

### 5.9. `agents.py` - Enhanced Specialized AI Agents

This file defines the abstract `BaseAgent` and the concrete agent implementations with enhanced thinking capabilities.

-   **`BaseAgent` (ABC):** Enhanced abstract base class with AI client integration for thinking budget functionality.
    -   `_initialize_model()`: Configures Gemini API and initializes generative models
    -   `_initialize_ai_client()`: Sets up AI client for thinking budget capabilities
    -   `_generate_response()`: Common method for standard AI generation
-   **`PlannerAgent`:** Enhanced planning agent with thinking budget support.
    -   `process()`: Accepts difficulty percentage parameter for thinking budget allocation
    -   Uses Flash 2.5 with thinking for complex tasks (≥50% difficulty)
    -   Produces JSON array of steps with descriptions and commands
-   **`ExecutorAgent`:** Generates single, non-interactive shell commands from step descriptions.
-   **`RefinerAgent`:** Core self-correction mechanism for command failure recovery.
    -   Analyzes failed commands with error output
    -   Generates corrected commands or alternative approaches
-   **`SummarizerAgent`:** Creates concise, human-readable summaries of completed steps.
-   **`ErrorRecoveryPlanner`:** Creates multi-step recovery plans for fundamental command failures.

### 5.10. `models.py` - Enhanced Data Models

This file contains all Pydantic models used for API requests/responses and for internal state management.

-   **Request/Response Models:**
    -   `StartRequest`, `MessageRequest`: Define the structure of incoming API requests.
    -   `TaskStatusResponse`: Defines the rich structure of the `/task/{task_id}` endpoint response, including orchestrator-specific fields.
    -   `SSHResult`: A structured model for the outcome of an SSH command.
-   **Orchestrator Models:**
    -   `TaskStatus`, `StepStatus`: Enums for tracking the state of tasks and steps.
    -   `TaskStep`: Represents a single step in a plan, with its description, command, status, and execution attempts.
    -   `TaskPlan`: Contains the full list of `TaskStep` objects for a task.
    -   `StepAttempt`: Records the details of a single attempt to execute a step's command, including the `SSHResult`.
-   **Agent Response Models:**
    -   `AgentResponse` (Base), `PlannerResponse`, `ExecutorResponse`, `RefinerResponse`, `SummarizerResponse`, `ErrorRecoveryResponse`: Structured models for the output of each AI agent, ensuring type safety and consistency.
-   **Core Internal Model:**
    -   `TaskEntry`: The most important internal model with enhanced intent detection support. It represents a single task in the `TaskManager` and holds all its state, including:
        -   Gemini chat object and conversation history
        -   Task status and system information
        -   Orchestrator fields (`task_plan`, `main_context_history`, `current_step_attempts`)
        -   **New:** `task_difficulty_percentage` for thinking budget allocation

## 6. New Features: Intent Detection & Thinking Budget System

### 6.1. Intelligent Intent Detection

The system now uses **Gemma-27b** to intelligently classify user messages before processing:

#### **Chat Intent Detection**
- **Trigger**: Casual conversation, greetings, questions about the system
- **Response Format**: `#Chat:(direct response message)`
- **Behavior**: Immediate response without planning or execution
- **Examples**:
  - "Hello, how are you?" → Direct chat response
  - "What can you help me with?" → Capability explanation

#### **VPS Intent Detection**
- **Trigger**: System administration tasks, server management requests
- **Response Format**: `#VPS:(0%-100%)` where percentage indicates task complexity
- **Behavior**: Proceeds to planning phase with thinking budget allocation
- **Examples**:
  - "Check disk space" → `#VPS:30%` (simple task)
  - "Set up complete LAMP stack with SSL" → `#VPS:85%` (complex task)

### 6.2. Thinking Budget System

For complex VPS tasks, the system leverages **Flash 2.5's thinking capabilities**:

#### **Thinking Budget Calculation**
- **Trigger**: Task difficulty ≥ 50% (configurable via `MIN_THINKING_PERCENTAGE`)
- **Formula**: `(percentage - 50) / 50 * 24576`
- **Range**: 50%-100% maps to 0-24576 thinking budget
- **Model**: `gemini-2.5-flash-preview-05-20`

#### **Resource Allocation Examples**
- **50% difficulty**: 0 thinking budget (standard generation)
- **75% difficulty**: 12,288 thinking budget (enhanced reasoning)
- **100% difficulty**: 24,576 thinking budget (maximum reasoning)

### 6.3. Enhanced User Experience

#### **Streamlined Interactions**
- **Chat messages**: Instant responses without unnecessary planning overhead
- **Simple VPS tasks**: Fast execution with standard AI generation
- **Complex VPS tasks**: Enhanced planning with superior reasoning capabilities

#### **Intelligent Resource Management**
- **Computational efficiency**: Resources allocated based on actual task complexity
- **Cost optimization**: Thinking budget only used when beneficial
- **Scalable architecture**: Percentage-based system allows fine-tuned control

### 6.4. Configuration

#### **New Environment Variables**
```bash
# Intent Detection
INTENT_MODEL=models/gemma-3-27b-it
USE_LLM_INTENT=true

# Thinking Budget
FLASH_MODEL=gemini-2.5-flash-preview-05-20
USE_THINKING=true
MIN_THINKING_PERCENTAGE=50
```

#### **Fallback Mechanisms**
- **Intent detection failure**: Falls back to keyword-based detection
- **Thinking budget unavailable**: Falls back to standard generation
- **Model unavailable**: Graceful degradation to alternative models

## 7. Included README (`ORCHESTRATOR_README.md`)

This section contains the original, verbatim content from `ORCHESTRATOR_README.md` for reference.

---

# VPS Admin Orchestrator System

## Overview

The VPS Admin system has been enhanced with a sophisticated multi-component orchestrator that replaces the single reactive agent with a more intelligent, step-by-step task execution system.

## Architecture

### Core Components

1.  **TaskOrchestrator** - The central "brain" that manages task execution
2.  **PlannerAgent** - Breaks down user requests into executable steps
3.  **ExecutorAgent** - Converts steps into specific shell commands
4.  **RefinerAgent** - Analyzes failures and suggests corrections
5.  **SummarizerAgent** - Creates concise summaries of completed steps

### Execution Flow

1.  **Planning Phase**: User request is analyzed and broken into structured steps
2.  **Execution Phase**: Each step is executed with automatic retry and refinement
3.  **Self-Correction**: Failed commands are analyzed and alternative approaches are tried
4.  **Progress Tracking**: Detailed progress updates and context management

## Key Features

### Intelligent Task Decomposition

-   Automatically breaks complex tasks into logical steps
-   Considers dependencies and prerequisites
-   Provides clear progress tracking

### Self-Correcting Execution

-   Analyzes command failures and suggests fixes
-   Tries alternative approaches when commands fail
-   Learns from previous attempts within the same step

### Enhanced Context Management

-   Maintains main context history of successful steps
-   Scoped memory for current step attempts
-   Rich progress tracking and metadata

### Security-Aware

-   Analyzes commands for security risks
-   Categorizes command types (package management, service management, etc.)
-   Provides security warnings for potentially dangerous operations

## Configuration

### Environment Variables

```bash
# Enable/disable orchestrator system
USE_ORCHESTRATOR=true

# Maximum retries per step
MAX_RETRIES_PER_STEP=3

# Orchestrator timeout (seconds)
ORCHESTRATOR_TIMEOUT=300
```

### Task-Level Control

You can enable/disable orchestrator mode per task:

```bash
# Enable orchestrator for a specific task
POST /task/{task_id}/orchestrator/enable

# Disable orchestrator for a specific task
POST /task/{task_id}/orchestrator/disable
```

## API Enhancements

### Enhanced Task Status Response

The task status endpoint now includes orchestrator-specific fields:

```json
{
  "task_id": "uuid",
  "status": "EXECUTING",
  "use_orchestrator": true,
  "task_plan": {
    "steps": [
      {
        "step_number": 1,
        "description": "Update package lists",
        "status": "COMPLETED",
        "command": "sudo apt update",
        "summary": "Successfully updated package lists",
        "attempts": 1
      }
    ],
    "total_steps": 5,
    "current_step": 1
  },
  "main_context_history": [
    "Step 1: Successfully updated package lists"
  ],
  "current_step_attempts": 0,
  "final_output": ""
}
```

### New Event Types

The orchestrator system introduces new SSE event types:

-   `plan_created` - When a task plan is generated
-   `step_start` - When a step begins execution
-   `step_complete` - When a step completes successfully
-   `task_failed` - When a task fails after all retries
-   `task_complete` - When all steps complete successfully

## Backward Compatibility

The system maintains full backward compatibility:

-   Legacy tasks continue to work with the original system
-   New tasks use the orchestrator by default (configurable)
-   All existing API endpoints remain unchanged
-   Frontend integration requires no changes

## Benefits

### For Users

-   **Clearer Progress**: Step-by-step progress with detailed feedback
-   **Better Reliability**: Automatic retry and error correction
-   **Enhanced Security**: Built-in security analysis and warnings
-   **Improved Transparency**: Clear understanding of what's happening

### For Developers

-   **Modular Design**: Specialized agents for different tasks
-   **Extensible**: Easy to add new agent types or capabilities
-   **Testable**: Individual components can be tested in isolation
-   **Maintainable**: Clear separation of concerns

## Example Usage

### Simple Task

```javascript
User: "Install Docker"

Orchestrator Flow:
1. Planning: Breaks into steps (update packages, install prerequisites, add GPG key, etc.)
2. Execution: Executes each step with automatic retry
3. Self-Correction: If a step fails, analyzes error and tries alternative approach
4. Completion: Provides summary of what was accomplished
```

### Complex Task with Failures

```javascript
User: "Set up a web server with SSL"

Orchestrator Flow:
1. Planning: Creates 8-step plan
2. Step 3 fails: "Install Nginx" fails due to missing repository
3. Refiner analyzes: Suggests adding repository first
4. Retry succeeds: Nginx installed successfully
5. Continues with remaining steps
6. Completion: Full web server with SSL configured
```

## Monitoring and Debugging

### Logs

-   Detailed logging for each orchestrator component
-   Step-by-step execution tracking
-   Error analysis and retry attempts

### Status Tracking

-   Real-time progress updates
-   Detailed step status and attempt history
-   Context preservation across steps

## Future Enhancements

-   **Learning System**: Learn from successful patterns across tasks
-   **Parallel Execution**: Execute independent steps in parallel
-   **Rollback Capability**: Automatic rollback on critical failures
-   **Custom Agents**: Plugin system for domain-specific agents